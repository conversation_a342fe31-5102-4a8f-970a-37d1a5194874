from maix import image, display, app, time, camera
import cv2
import numpy as np
import os

# HSV参数文件路径
HSV_FILE_PATH = "/root/hsv.txt"

def load_hsv_parameters(file_path):
    """从文件加载HSV参数"""
    hsv_params = {
        'h_min': 0, 'h_max': 180,
        's_min': 0, 's_max': 255,
        'v_min': 0, 'v_max': 255
    }
    
    try:
        if not os.path.exists(file_path):
            print(f"[警告] HSV参数文件不存在: {file_path}，使用默认参数")
            return hsv_params
            
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                key, value = line.split('=')
                if key in hsv_params:
                    hsv_params[key] = int(value)
            
            print(f"[成功] 已从 {file_path} 加载HSV参数")
            return hsv_params
    
    except Exception as e:
        print(f"[错误] 加载HSV参数失败: {e}，使用默认参数")
        return hsv_params

def detect_object(img, hsv_params):
    """使用HSV参数检测目标"""
    # 转换颜色空间为HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # 创建二值化图像
    lower = np.array([hsv_params['h_min'], hsv_params['s_min'], hsv_params['v_min']])
    upper = np.array([hsv_params['h_max'], hsv_params['s_max'], hsv_params['v_max']])
    mask = cv2.inRange(hsv, lower, upper)
    
    # 闭运算，消除小噪点
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    
    # 寻找外轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    object_coords = []
    
    for contour in contours:
        # 计算轮廓面积，过滤小面积区域
        area = cv2.contourArea(contour)
        if area < 100:  # 可调整此阈值
            continue
            
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 提取中心坐标
        center = tuple(map(int, rect[0]))
        object_coords.append(center)
        
        # 在原图上绘制中心点和轮廓
        cv2.circle(img, center, 5, (0, 255, 0), -1)
        cv2.drawContours(img, [contour], -1, (0, 255, 0), 2)
    
    return object_coords, mask

def draw_hsv_info(img, hsv_params):
    """在图像上显示HSV参数信息"""
    y_offset = 30
    for i, (key, value) in enumerate(hsv_params.items()):
        cv2.putText(img, f"{key}: {value}", (10, y_offset * (i + 1)),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

def main():
    # 加载HSV参数
    hsv_params = load_hsv_parameters(HSV_FILE_PATH)
    
    # 初始化摄像头和显示
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    
    try:
        while not app.need_exit():
            # 捕获图像
            img = cam.read()
            img = image.image2cv(img, ensure_bgr=False, copy=False)
            
            # 检测目标
            object_coords, mask = detect_object(img, hsv_params)
            
            # 在原图上显示HSV参数
            draw_hsv_info(img, hsv_params)
            
            # 显示检测到的目标数量
            cv2.putText(img, f"Objects: {len(object_coords)}", (10, 210),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 显示处理后的图像
            img_show = image.cv2image(img, bgr=True, copy=False)
            disp.show(img_show)
            
    except Exception as e:
        print(f"[错误] 运行时错误: {e}")
    finally:
        try:
            cam.release()
            print("[调试] 相机资源已释放")
        except:
            pass

if __name__ == "__main__":
    main()