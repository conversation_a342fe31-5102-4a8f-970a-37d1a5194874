# 串口功能测试验证清单

## 测试环境准备

### 硬件要求
- [ ] MaixCAM设备正常工作
- [ ] 串口连接正确（TX、RX、GND）
- [ ] 串口设备路径确认为 `/dev/ttyS0`

### 软件要求
- [ ] main.py 主程序可正常运行
- [ ] serial_test.py 测试脚本准备就绪
- [ ] MaixPy环境配置正确

## 功能测试项目

### 1. 数据包格式验证
- [ ] 数据包内容：`0x78, 0x15, 0x00, 0x00, 0x00, 0x00, 0xfc`
- [ ] 数据包长度：7字节
- [ ] 数据包格式：bytes类型
- [ ] 十六进制显示正确

### 2. 发送间隔测试
- [ ] 目标间隔：5ms
- [ ] 允许误差：±1ms (4ms-6ms)
- [ ] 平均间隔在范围内
- [ ] 间隔稳定性良好

### 3. 持续时间测试
- [ ] 目标持续时间：2秒
- [ ] 允许误差：±50ms (1950ms-2050ms)
- [ ] 自动停止机制正常
- [ ] 发送计数准确

### 4. UI界面测试
- [ ] START按钮功能正常
- [ ] STOP按钮功能正常
- [ ] 串口状态显示正确
- [ ] 状态颜色变化正常（绿色=运行，白色=停止）
- [ ] 临时消息提示正常

### 5. 系统集成测试
- [ ] 串口功能不影响HSV调整功能
- [ ] 多线程运行稳定
- [ ] 资源释放正确
- [ ] 无内存泄漏

## 性能测试

### 1. 精度测试
- [ ] 发送间隔精度 ≤ ±1ms
- [ ] 持续时间精度 ≤ ±50ms
- [ ] 数据包计数准确

### 2. 稳定性测试
- [ ] 连续多次启动/停止正常
- [ ] 长时间运行无异常
- [ ] 异常情况恢复正常

### 3. 兼容性测试
- [ ] 与现有功能无冲突
- [ ] UI响应正常
- [ ] 系统性能无明显影响

## 测试步骤

### 步骤1：基础功能测试
1. 启动main.py主程序
2. 观察界面显示是否正常
3. 点击START按钮
4. 观察串口状态变化
5. 运行serial_test.py测试脚本
6. 检查测试结果

### 步骤2：精度验证
1. 记录发送间隔数据
2. 计算平均值、最大值、最小值
3. 验证是否在允许范围内
4. 记录持续时间
5. 验证自动停止功能

### 步骤3：UI交互测试
1. 测试START/STOP按钮切换
2. 观察状态显示变化
3. 验证临时消息提示
4. 测试与其他功能的兼容性

## 验收标准

### 必须通过项目
- [x] 数据包格式100%正确
- [x] 发送间隔误差 ≤ ±1ms
- [x] 持续时间误差 ≤ ±50ms
- [x] 自动停止机制正常
- [x] UI功能完全正常

### 性能要求
- [x] CPU占用率合理
- [x] 内存使用稳定
- [x] 无资源泄漏
- [x] 系统响应正常

## 测试结果记录

### 测试日期：_______
### 测试人员：_______
### 测试环境：_______

### 测试结果：
- 数据包格式：□ 通过 □ 失败
- 发送间隔：□ 通过 □ 失败 (实际值: ___ms)
- 持续时间：□ 通过 □ 失败 (实际值: ___ms)
- UI功能：□ 通过 □ 失败
- 系统集成：□ 通过 □ 失败

### 问题记录：
_________________________________
_________________________________
_________________________________

### 总体评价：□ 通过 □ 需要修改
