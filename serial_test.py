#!/usr/bin/env python3
# 串口功能测试脚本

from maix import uart, time
import threading

# 测试配置
SERIAL_DEVICE = "/dev/ttyS0"
SERIAL_BAUDRATE = 115200
TEST_DATA_PACKET = b'\x78\x15\x00\x00\x00\x00\xfc'

def test_serial_receive():
    """测试串口接收功能"""
    try:
        serial = uart.UART(SERIAL_DEVICE, SERIAL_BAUDRATE)
        print("[测试] 开始监听串口数据...")
        
        start_time = time.ticks_ms()
        packet_count = 0
        last_packet_time = 0
        intervals = []  # 记录间隔时间
        
        while time.ticks_ms() - start_time < 5000:  # 监听5秒
            data = serial.read(timeout=100)
            if data:
                current_time = time.ticks_ms()
                packet_count += 1
                
                if packet_count > 1:
                    interval = current_time - last_packet_time
                    intervals.append(interval)
                    print(f"[接收] 数据包{packet_count}: {data.hex()}, 间隔: {interval}ms")
                else:
                    print(f"[接收] 数据包{packet_count}: {data.hex()}")
                
                last_packet_time = current_time
                
                # 验证数据包格式
                if data == TEST_DATA_PACKET:
                    print("[验证] 数据包格式正确")
                else:
                    print(f"[错误] 数据包格式错误，期望: {TEST_DATA_PACKET.hex()}, 实际: {data.hex()}")
        
        # 分析测试结果
        print(f"\n[结果] 总共接收到{packet_count}个数据包")
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            min_interval = min(intervals)
            max_interval = max(intervals)
            print(f"[间隔分析] 平均间隔: {avg_interval:.2f}ms")
            print(f"[间隔分析] 最小间隔: {min_interval}ms")
            print(f"[间隔分析] 最大间隔: {max_interval}ms")
            
            # 检查间隔精度
            if 4 <= avg_interval <= 6:
                print("[验证] 发送间隔符合5ms±1ms要求")
            else:
                print(f"[错误] 发送间隔不符合要求，期望5ms±1ms，实际{avg_interval:.2f}ms")
        
        # 检查发送持续时间（基于数据包数量估算）
        if packet_count > 0:
            estimated_duration = packet_count * 5  # 每5ms一个包
            print(f"[持续时间分析] 估算发送持续时间: {estimated_duration}ms")
            if 1950 <= estimated_duration <= 2050:  # 2秒±50ms
                print("[验证] 发送持续时间符合2秒±50ms要求")
            else:
                print(f"[错误] 发送持续时间不符合要求，期望2000ms±50ms，实际约{estimated_duration}ms")
        
        serial.close()
        
    except Exception as e:
        print(f"[错误] 测试失败: {e}")

def test_data_packet_format():
    """测试数据包格式验证"""
    print("\n[格式测试] 验证数据包格式...")
    expected = b'\x78\x15\x00\x00\x00\x00\xfc'
    print(f"期望数据包: {expected.hex()}")
    print(f"数据包长度: {len(expected)} 字节")
    print(f"各字节值: {[hex(b) for b in expected]}")

def print_test_instructions():
    """打印测试说明"""
    print("=" * 50)
    print("串口功能测试脚本")
    print("=" * 50)
    print("测试说明:")
    print("1. 确保主程序(main.py)正在运行")
    print("2. 在主程序中点击START按钮启动串口发送")
    print("3. 运行此测试脚本监听串口数据")
    print("4. 观察测试结果和验证报告")
    print("-" * 50)

if __name__ == "__main__":
    print_test_instructions()
    test_data_packet_format()
    test_serial_receive()
